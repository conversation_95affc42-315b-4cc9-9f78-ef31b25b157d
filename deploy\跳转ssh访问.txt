跳转ssh访问
	


// 访问********
ssh root@********


// 访问********
ssh root@********

sftp root@********
sftp> put ./backend_admin_server:1.0.tar /data/backend/backned_admin_server/
CA-CQUPT.20250717
# 使用 scp 上传文件
scp /data/tmp/opendjdk_8-jre-slim.tar root@********:/data/tmp/opendjdk_8-jre-slim.tar

CA-CQUPT.20250717


root@ecm-large-node1:~# curl -v -u elastic:es123 http://********:9200
*   Trying ********:9200...
* Connected to ******** (********) port 9200 (#0)
* Server auth using Basic with user 'elastic'
> GET / HTTP/1.1
> Host: ********:9200
> Authorization: Basic ZWxhc3RpYzpDaGFuZ2FuNWcuZXM=
> User-Agent: curl/7.81.0
> Accept: */*
> 
* Mark bundle as not supporting multiuse
< HTTP/1.1 200 OK
< X-elastic-product: Elasticsearch
< content-type: application/json; charset=UTF-8
< content-length: 538
< 
{
  "name" : "es-node-1",
  "cluster_name" : "es-cluster",
  "cluster_uuid" : "dPfiTrKSQYWcHs0-UOZTwg",