package com.unnet.changan5G.service;

import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 终端缓存服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface TerminalCacheService {

    /**
     * 缓存终端基本信息
     */
    void cacheTerminalBasicInfo(String deviceId, TerminalBasicInfo basicInfo);

    /**
     * 获取缓存的终端基本信息
     */
    TerminalBasicInfo getCachedTerminalBasicInfo(String deviceId);

    /**
     * 删除终端基本信息缓存
     */
    void removeCachedTerminalBasicInfo(String deviceId);

    /**
     * 缓存终端最后上报时间
     */
    void cacheLastReportTime(String deviceId, LocalDateTime reportTime);

    /**
     * 获取缓存的最后上报时间
     */
    LocalDateTime getCachedLastReportTime(String deviceId);

    /**
     * 批量获取最后上报时间
     */
    List<LocalDateTime> batchGetLastReportTime(List<String> deviceIds);

    /**
     * 缓存终端在线状态
     */
    void cacheDeviceStatus(String deviceId, Integer status);

    /**
     * 获取缓存的设备状态
     */
    Integer getCachedDeviceStatus(String deviceId);

    /**
     * 批量更新设备状态
     */
    void batchUpdateDeviceStatus(List<String> deviceIds, Integer status);

    /**
     * 获取所有在线设备ID
     */
    Set<String> getOnlineDeviceIds();

    /**
     * 获取所有离线设备ID
     */
    Set<String> getOfflineDeviceIds();

    /**
     * 检查设备是否存在于缓存中
     */
    boolean isDeviceExistsInCache(String deviceId);

    /**
     * 设置设备注册标记
     */
    void markDeviceAsRegistered(String deviceId);

    /**
     * 检查设备是否已注册
     */
    boolean isDeviceRegistered(String deviceId);

    /**
     * 清理过期的缓存数据
     */
    void cleanExpiredCache();

    /**
     * 获取缓存统计信息
     */
    Object getCacheStatistics();

    /**
     * 预热缓存（从数据库加载数据到缓存）
     */
    void warmUpCache();
}
