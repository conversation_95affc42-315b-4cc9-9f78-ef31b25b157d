package com.unnet.changan5G.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import com.unnet.changan5G.service.TerminalRegistrationService;
import com.unnet.changan5G.service.DeviceNotificationService;
import com.unnet.changan5G.service.TerminalBasicInfoService;
import com.unnet.changan5G.service.TerminalCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 终端状态检查定时任务
 *
 * 功能：
 * 1. 每10秒检查一次离线设备
 * 2. 发送离线通知到SSE
 * 3. 更新数据库和缓存状态
 * 4. 推送统计信息更新
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TerminalStatusCheckTask {

    private final TerminalRegistrationService terminalRegistrationService;
    private final DeviceNotificationService deviceNotificationService;
    private final TerminalBasicInfoService terminalBasicInfoService;
    private final TerminalCacheService terminalCacheService;

    /**
     * 检查离线设备定时任务
     * 每10秒执行一次，检查是否有设备离线
     *
     * 改进功能：
     * 1. 检查离线设备并发送通知
     * 2. 更新数据库和缓存状态
     * 3. 推送统计信息更新
     */
    @Scheduled(fixedRate = 10000) // 10秒
    public void checkOfflineDevices() {
        try {
            log.info("=== 开始检查离线设备 ===");

            // 执行离线设备检查并获取离线设备列表
            var offlineDevices = checkOfflineDevicesFromCache();

            // 处理新发现的离线设备
            if (!offlineDevices.isEmpty()) {
                log.warn("发现 {} 个新离线设备，准备发送SSE通知", offlineDevices.size());

                for (var deviceInfo : offlineDevices) {
                    String deviceId = deviceInfo.getDeviceId();
                    String hostname = deviceInfo.getHostname();

                    try {
                        // 检查设备当前数据库状态，避免重复处理
                        var currentEntity = terminalBasicInfoService.getByDeviceId(deviceId);
                        if (currentEntity != null && currentEntity.getStatus() == 0) {
                            log.debug("设备已经是离线状态，跳过处理 - 设备: {}", deviceId);
                            continue;
                        }

                        // 1. 更新数据库状态为离线
                        boolean updateResult = terminalBasicInfoService.updateDeviceStatus(deviceId, 0);
                        if (!updateResult) {
                            log.error("更新设备离线状态失败 - 设备: {}", deviceId);
                            continue;
                        }

                        // 2. 更新Redis缓存状态
                        terminalCacheService.cacheDeviceStatus(deviceId, 0);

                        // 3. 发送离线通知到SSE（使用MAC地址）
                        String identityMac = currentEntity.getIdentityMac();
                        log.info("准备发送设备离线SSE通知 - 设备MAC: {}, 主机名: {}", identityMac, hostname);
                        deviceNotificationService.sendDeviceOfflineNotification(identityMac, hostname);
                        log.info("设备离线SSE通知已发送 - 设备MAC: {}, 主机名: {}", identityMac, hostname);

                        log.warn("设备离线处理完成 - 设备: {}, 主机名: {}", deviceId, hostname);

                    } catch (Exception e) {
                        log.error("处理离线设备失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
                    }
                }

                // 4. 发送统计信息更新通知
                sendStatisticsUpdateNotification();
            } else {
                log.debug("未发现新的离线设备");
            }

            // 获取设备统计信息用于日志
            long onlineCount = terminalRegistrationService.getOnlineDeviceCount();
            long offlineCount = terminalRegistrationService.getOfflineDeviceCount();

            log.debug("设备状态统计 - 在线: {}, 离线: {}", onlineCount, offlineCount);

        } catch (Exception e) {
            log.error("检查离线设备时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 设备状态统计定时任务
     * 每分钟执行一次，输出设备状态统计信息
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void logDeviceStatistics() {
        try {
            long onlineCount = terminalRegistrationService.getOnlineDeviceCount();
            long offlineCount = terminalRegistrationService.getOfflineDeviceCount();
            long totalCount = onlineCount + offlineCount;

            if (totalCount > 0) {
                log.info("设备状态统计 - 总数: {}, 在线: {} ({}%), 离线: {} ({}%)",
                        totalCount,
                        onlineCount, String.format("%.1f", (onlineCount * 100.0 / totalCount)),
                        offlineCount, String.format("%.1f", (offlineCount * 100.0 / totalCount)));
            } else {
                log.info("当前无设备注册");
            }

        } catch (Exception e) {
            log.error("输出设备统计信息时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 从Redis缓存和数据库检查离线设备
     * 这个方法结合Redis缓存和数据库数据，确保准确检测离线设备
     */
    private List<TerminalBasicInfo> checkOfflineDevicesFromCache() {
        List<TerminalBasicInfo> newOfflineDevices = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        try {
            // 方案1：从Redis获取在线设备ID
            Set<String> onlineDeviceIds = terminalCacheService.getOnlineDeviceIds();
            log.debug("Redis中在线设备数量: {}", onlineDeviceIds.size());

            // 方案2：从数据库获取状态为1的设备作为补充
            var onlineEntitiesFromDB = terminalBasicInfoService.list(
                new QueryWrapper<TerminalBasicInfoEntity>()
                    .eq("status", 1)
                    .eq("is_deleted", 0)
            );

            Set<String> dbOnlineDeviceIds = Collections.emptySet();
            if (onlineEntitiesFromDB != null && !onlineEntitiesFromDB.isEmpty()) {
                dbOnlineDeviceIds = onlineEntitiesFromDB.stream()
                    .map(TerminalBasicInfoEntity::getDeviceId)
                    .collect(Collectors.toSet());
                log.debug("从数据库获取到在线设备数量: {}", dbOnlineDeviceIds.size());
            }

            // 合并Redis和数据库的在线设备ID（取并集）
            onlineDeviceIds.addAll(dbOnlineDeviceIds);

            if (onlineDeviceIds.isEmpty()) {
                log.debug("Redis和数据库中都无在线设备");
                return newOfflineDevices;
            }

            log.debug("合并后的在线设备总数: {}", onlineDeviceIds.size());

            // 2. 检查每个在线设备的最后上报时间
            for (String deviceId : onlineDeviceIds) {
                try {
                    // 从Redis获取最后上报时间
                    LocalDateTime lastReportTime = terminalCacheService.getCachedLastReportTime(deviceId);

                    // 如果Redis中没有，从数据库获取最后更新时间
                    if (lastReportTime == null) {
                        log.debug("Redis中没有设备最后上报时间，从数据库获取 - 设备: {}", deviceId);
                        var entity = terminalBasicInfoService.getByDeviceId(deviceId);
                        if (entity != null && entity.getLastUpdateTime() != null) {
                            lastReportTime = entity.getLastUpdateTime();
                            log.debug("从数据库获取到最后更新时间 - 设备: {}, 时间: {}", deviceId, lastReportTime);
                        } else {
                            log.warn("数据库中也没有设备最后更新时间，跳过检查 - 设备: {}", deviceId);
                            continue;
                        }
                    }

                    // 计算离线时长
                    long secondsSinceLastReport = Duration.between(lastReportTime, now).getSeconds();
                    log.debug("设备 {} 最后上报时间: {}, 离线时长: {}秒", deviceId, lastReportTime, secondsSinceLastReport);

                    // 判断是否超过离线阈值（30秒）
                    if (secondsSinceLastReport > 30) {
                        // 获取设备基本信息
                        TerminalBasicInfo basicInfo = terminalCacheService.getCachedTerminalBasicInfo(deviceId);

                        if (basicInfo == null) {
                            // 如果Redis中没有，从数据库获取
                            var entity = terminalBasicInfoService.getByDeviceId(deviceId);
                            if (entity != null) {
                                basicInfo = new TerminalBasicInfo();
                                BeanUtils.copyProperties(entity, basicInfo);
                            }
                        }

                        if (basicInfo != null) {
                            // 设置为离线状态
                            basicInfo.setStatus(0);
                            basicInfo.setLastUpdateTime(now);

                            newOfflineDevices.add(basicInfo);

                            log.warn("检测到设备离线 - 设备: {}, 主机名: {}, 最后上报: {}, 离线时长: {}秒",
                                    deviceId, basicInfo.getHostname(), lastReportTime, secondsSinceLastReport);
                        }
                    }

                } catch (Exception e) {
                    log.error("检查设备离线状态失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("从缓存检查离线设备失败: {}", e.getMessage(), e);
        }

        return newOfflineDevices;
    }

    /**
     * 发送统计信息更新通知
     */
    private void sendStatisticsUpdateNotification() {
        try {
            // 获取最新的统计信息
            int onlineCount = terminalBasicInfoService.countOnlineDevices();
            int offlineCount = terminalBasicInfoService.countOfflineDevices();
            int alertCount = 0; // 这里可以根据需要获取告警数量

            // 发送统计信息更新通知
            deviceNotificationService.sendStatisticsUpdateNotification(
                    onlineCount, offlineCount, alertCount);

            log.debug("发送统计信息更新通知 - 在线: {}, 离线: {}, 告警: {}",
                    onlineCount, offlineCount, alertCount);

        } catch (Exception e) {
            log.error("发送统计信息更新通知失败: {}", e.getMessage(), e);
        }
    }
}
