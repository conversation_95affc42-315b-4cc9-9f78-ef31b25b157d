spring:
  application:
    name: 长安5G管理平台-数据收集服务
  main:
    allow-bean-definition-overriding: true
  profiles:
    # 环境切换配置
    # dev: 开发环境
    # prod: 生产环境
    active: ${SPRING_PROFILES_ACTIVE:prod}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

#  # Elasticsearch配置
#  elasticsearch:
#    username: elastic
#    password: 123456
#    uris: *************:9200
#
#  # Redis配置
#  redis:
#    # 地址
#    host: *************
#    port: 30379
#    database: 0
#    # 密码
#    password: password4redis
#    # 连接超时时间
#    timeout: 10s
#    lettuce:
#      pool:
#        # 连接池中的最小空闲连接
#        min-idle: 0
#        # 连接池中的最大空闲连接
#        max-idle: 8
#        # 连接池的最大数据库连接数
#        max-active: 8
#        # #连接池最大阻塞等待时间（使用负值表示没有限制）
#        max-wait: -1ms



server:
  port: 8081
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  # 支持HTTP和HTTPS
  forward-headers-strategy: native
  # Tomcat配置
  tomcat:
    uri-encoding: UTF-8
    max-connections: 8192
    threads:
      max: 200
      min-spare: 10

# 日志配置
logging:
  level:
    root: INFO
    com.unnet.changan5G: DEBUG  # 修正包名，启用AOP日志
    org.springframework.web: INFO

# Swagger配置
springdoc:
  api-docs:
    enabled: true  # 是否开启API文档
    path: /v3/api-docs  # API文档路径
  swagger-ui:
    enabled: true  # 是否开启Swagger UI界面
    path: /swagger-ui.html  # Swagger UI路径
  packages-to-scan: com.unnet.changan5G.controller  # 要扫描的包
  paths-to-match: /**, /demo/**, /api/demo/**  # 要匹配的路径
  cache:
    disabled: true
