package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.MetricRequestBody;
import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.dto.terminal.TerminalMetricInfo;
import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 指标数据消费服务 - 分类处理终端基本信息、指标信息和告警信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetricConsumerService {

    private final ObjectMapper objectMapper;
    private final MetricDataClassificationService classificationService;
    private final TerminalRegistrationService terminalRegistrationService;
    private final TerminalBasicInfoService terminalBasicInfoService;
    private final TerminalMetricInfoService terminalMetricInfoService;
    private final TerminalAlertInfoService terminalAlertInfoService;
    private final TerminalMetricElasticsearchService terminalMetricElasticsearchService;
    private final TerminalCacheService terminalCacheService;
    private final DeviceNotificationService deviceNotificationService;
    private final DynamicMetricAlertService dynamicMetricAlertService;

    private final MetricThresholdConfigService metricThresholdConfigService;

    /**
     * 监听5g_metric主题，消费指标数据并进行分类处理
     */
    @KafkaListener(topics = "5g_metric")
    public void consumeAndClassifyMetricData(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
            @Header(KafkaHeaders.RECEIVED_MESSAGE_KEY) String key,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {
        
        try {
            log.info("接收到Kafka消息 - Topic: {}, Partition: {}, Offset: {}, Key: {}",
                    topic, partition, offset, key);
            log.info("消息内容: {}", message);

            // 解析包含时间戳的消息数据
            JsonNode messageNode = objectMapper.readTree(message);
            String originalData = messageNode.get("originalData").asText();
            String metricCollectTimeStr = messageNode.get("metricCollectTime").asText();
            String receiveTimeStr = messageNode.get("receiveTime").asText();

            // 解析原始JSON数据为MetricRequestBody对象
            MetricRequestBody metricData = objectMapper.readValue(originalData, MetricRequestBody.class);

            // 数据清洗：处理负值指标数据
            String cleanedOriginalData = cleanNegativeMetricValues(originalData);
            log.info("清洗后的数据为："+cleanedOriginalData);
            MetricRequestBody cleanedMetricData = objectMapper.readValue(cleanedOriginalData, MetricRequestBody.class);

            // 解析时间字符串为LocalDateTime
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime metricCollectTime = LocalDateTime.parse(metricCollectTimeStr, formatter);
            LocalDateTime receiveTime = LocalDateTime.parse(receiveTimeStr, formatter);

            // 格式化MAC地址
            String formattedMac = formatMacAddress(metricData.getIdentityMac());

            log.info("成功解析并清洗指标数据 - 主机名: {}, 设备ID: {}, MAC地址: {} (原始: {}), 采集时间: {}",
                    cleanedMetricData.getHostname(),
                    cleanedMetricData.getDeviceId(),
                    formattedMac,
                    cleanedMetricData.getIdentityMac(),
                    metricCollectTimeStr);

            // 在Kafka消费端处理终端注册和状态管理（使用格式化的MAC地址和清洗后的数据）
            TerminalRegistrationService.TerminalRegistrationResult registrationResult =
                terminalRegistrationService.processTerminalRegistration(cleanedMetricData, formattedMac, metricCollectTime);

            log.info("=== 终端注册处理结果 ===");
            log.info("设备MAC: {}, 需要注册: {}, 状态变化: {} ({})",
                    formattedMac,
                    registrationResult.isNeedsRegistration(),
                    registrationResult.getStatusChange().name(),
                    registrationResult.getStatusChange().getDescription());

            // 分类处理数据，传递清洗后的数据和时间戳、注册信息（使用格式化的MAC地址）
            classifyAndProcessData(cleanedOriginalData, cleanedMetricData, formattedMac, metricCollectTime, receiveTime,
                    registrationResult.isNeedsRegistration(),
                    registrationResult.getStatusChange().name(),
                    registrationResult.getBasicInfo());
            
            // 手动确认消息已处理
            acknowledgment.acknowledge();
            log.debug("消息处理完成并已确认 - Offset: {}", offset);
            
        } catch (Exception e) {
            log.error("处理Kafka消息失败 - Topic: {}, Partition: {}, Offset: {}, Key: {}, 错误: {}", 
                    topic, partition, offset, key, e.getMessage(), e);
            
            // 根据业务需求决定是否确认消息
            // 如果不确认，消息会重新投递
            // acknowledgment.acknowledge(); // 可以选择确认以避免重复处理
        }
    }

    /**
     * 分类处理指标数据
     */
    private void classifyAndProcessData(String originalData, MetricRequestBody metricData, String formattedMac,
                                      LocalDateTime metricCollectTime, LocalDateTime receiveTime,
                                      boolean needsRegistration, String statusChange, TerminalBasicInfo basicInfo) {
        try {
            log.info("开始分类处理指标数据 - 设备MAC: {}, 采集时间: {}, 需要注册: {}, 状态变化: {}",
                    formattedMac, metricCollectTime, needsRegistration, statusChange);

            // 1. 处理终端基本信息（如果需要注册或状态发生变化）
            if (needsRegistration || !"NO_CHANGE".equals(statusChange)) {
                processTerminalBasicInfo(basicInfo, needsRegistration, statusChange);
            }

            // 2. 提取并处理终端指标信息（使用格式化的MAC地址）
            TerminalMetricInfo metricInfo = classificationService.extractTerminalMetricInfo(metricData, formattedMac, metricCollectTime, receiveTime);
            String metricId = processTerminalMetricInfoAndReturnId(metricInfo);

            // 3. 检查并处理告警信息（使用动态告警检查）
            List<TerminalAlertInfo> alerts = dynamicMetricAlertService.checkAndGenerateAlerts(
                    originalData, formattedMac, metricCollectTime, metricId);
            processTerminalAlerts(alerts);

            log.info("指标数据分类处理完成 - 设备MAC: {}, 告警数量: {}, 注册状态: {}",
                    formattedMac, alerts.size(), needsRegistration ? "新注册" : "已注册");
            
        } catch (Exception e) {
            log.error("分类处理指标数据时发生错误 - 设备MAC: {}, 错误: {}",
                    formattedMac, e.getMessage(), e);
            throw e; // 重新抛出异常，让上层决定如何处理
        }
    }

    /**
     * 处理终端基本信息
     */
    private void processTerminalBasicInfo(TerminalBasicInfo basicInfo, boolean needsRegistration, String statusChange) {
        try {
            log.info("处理终端基本信息 - 设备: {}, 主机名: {}, MAC: {}, 授权过期: {}, 需要注册: {}, 状态变化: {}",
                    basicInfo.getDeviceId(), basicInfo.getHostname(),
                    basicInfo.getIdentityMac(), basicInfo.getExpiredDate(),
                    needsRegistration, statusChange);

            // 优化数据库业务逻辑，减少数据库操作
            if (needsRegistration) {
                // 1. 新设备注册：保存到终端基本信息表
                log.info("执行新设备注册 - 设备: {}", basicInfo.getDeviceId());

                // 先缓存到Redis（使用MAC地址作为key）
                terminalCacheService.cacheTerminalBasicInfo(basicInfo.getIdentityMac(), basicInfo);
                terminalCacheService.markDeviceAsRegistered(basicInfo.getIdentityMac());
                terminalCacheService.cacheDeviceStatus(basicInfo.getIdentityMac(), 1);

                // 异步保存到数据库
                boolean registerResult = terminalBasicInfoService.registerNewDevice(basicInfo);
                if (registerResult) {
                    log.info("新设备注册成功 - 设备: {}", basicInfo.getDeviceId());
                    // 2. 发送设备注册通知
                    sendDeviceRegistrationNotification(basicInfo);
                    // 3. 发送设备注册SSE通知（使用MAC地址）
                    deviceNotificationService.sendDeviceRegisteredNotification(
                            basicInfo.getIdentityMac(),
                            basicInfo.getHostname()
                    );
                } else {
                    log.error("新设备注册失败 - 设备: {}", basicInfo.getDeviceId());
                }
            } else {
                // 3. 更新现有设备信息（优先更新缓存）
                log.debug("更新现有设备信息 - 设备: {}", basicInfo.getDeviceId());

                // 先更新Redis缓存
                terminalCacheService.cacheTerminalBasicInfo(basicInfo.getDeviceId(), basicInfo);
                terminalCacheService.cacheLastReportTime(basicInfo.getDeviceId(), basicInfo.getReceiveTime());

                // 批量更新或延迟更新数据库（减少数据库写入频率）
                if (shouldUpdateDatabase(basicInfo.getDeviceId())) {
                    boolean updateResult = terminalBasicInfoService.saveOrUpdateTerminalInfo(basicInfo);
                    if (!updateResult) {
                        log.error("更新设备信息失败 - 设备: {}", basicInfo.getDeviceId());
                    }
                }
            }

            // 4. 处理状态变化（优先更新缓存）
            if (!"NO_CHANGE".equals(statusChange)) {
                handleDeviceStatusChange(basicInfo.getDeviceId(), statusChange, basicInfo.getHostname());
            }

        } catch (Exception e) {
            log.error("处理终端基本信息失败 - 设备: {}, 错误: {}",
                    basicInfo.getDeviceId(), e.getMessage(), e);
        }
    }

    /**
     * 处理终端指标信息
     */
    private void processTerminalMetricInfo(TerminalMetricInfo metricInfo) {
        try {
            log.info("处理终端指标信息 - 设备: {}, CPU温度: {}°C, CPU使用率: {}%, 内存使用率: {}%, 采集时间: {}",
                    metricInfo.getIdentityMac(), metricInfo.getCpuTemp(),
                    metricInfo.getCpuPercent(), metricInfo.getMemoryPercent(), metricInfo.getMetricTime());

            // 1. 保存到Elasticsearch（替代MySQL存储）
            boolean saveResult = terminalMetricElasticsearchService.saveMetricData(metricInfo);
            if (saveResult) {
                log.debug("终端指标信息保存到Elasticsearch成功 - 设备: {}", metricInfo.getIdentityMac());

                // 2. 更新设备最后上报时间（仍然更新MySQL中的基本信息）
                terminalBasicInfoService.updateLastReportTime(metricInfo.getIdentityMac(), metricInfo.getMetricTime());
            } else {
                log.error("终端指标信息保存到Elasticsearch失败 - 设备: {}", metricInfo.getIdentityMac());
            }

        } catch (Exception e) {
            log.error("处理终端指标信息失败 - 设备: {}, 错误: {}",
                    metricInfo.getIdentityMac(), e.getMessage(), e);
        }
    }

    /**
     * 处理终端指标信息并返回指标ID
     */
    private String processTerminalMetricInfoAndReturnId(TerminalMetricInfo metricInfo) {
        try {
            log.info("处理终端指标信息 - 设备MAC: {}, CPU温度: {}°C, CPU使用率: {}%, 内存使用率: {}%, 采集时间: {}",
                    metricInfo.getIdentityMac(), metricInfo.getCpuTemp(),
                    metricInfo.getCpuPercent(), metricInfo.getMemoryPercent(), metricInfo.getMetricTime());

            // 1. 保存到Elasticsearch并获取文档ID
            String metricId = terminalMetricElasticsearchService.saveMetricDataAndReturnId(metricInfo);
            if (metricId != null) {
                log.debug("终端指标信息保存到Elasticsearch成功 - 设备MAC: {}, 指标ID: {}", metricInfo.getIdentityMac(), metricId);

                // 2. 更新设备最后上报时间（使用MAC地址查询设备）
                terminalBasicInfoService.updateLastReportTimeByMac(metricInfo.getIdentityMac(), metricInfo.getMetricTime());

                return metricId;
            } else {
                log.error("终端指标信息保存到Elasticsearch失败 - 设备MAC: {}", metricInfo.getIdentityMac());
                return null;
            }

        } catch (Exception e) {
            log.error("处理终端指标信息失败 - 设备MAC: {}, 错误: {}",
                    metricInfo.getIdentityMac(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理终端告警信息
     */
    private void processTerminalAlerts(List<TerminalAlertInfo> alerts) {
        if (alerts == null || alerts.isEmpty()) {
            return;
        }
        try {
            log.info("处理终端告警信息 - 告警数量: {}", alerts.size());

            // 批量保存告警信息
            boolean batchSaveResult = terminalAlertInfoService.batchSaveAlertInfo(alerts);
            if (batchSaveResult) {
                log.info("批量保存告警信息成功 - 数量: {}", alerts.size());

                // 处理每个告警的后续逻辑
                for (TerminalAlertInfo alert : alerts) {
                    log.warn("处理告警 - 设备MAC: {}, 类型: {}, 详情: {}, 当前值: {}, 阈值: {}",
                            alert.getIdentityMac(), alert.getAlertType(), alert.getAlertDetails(),
                            alert.getCurrentValue(), alert.getThreshold());

                    // 1. 检查是否需要自动解决已恢复的告警
//                    terminalAlertInfoService.autoResolveRecoveredAlerts(alert.getDeviceId(), alert.getAlertType().name());

                    // 2. 发送告警SSE通知
                    sendAlertSSENotification(alert);
                }
            } else {
                log.error("批量保存告警信息失败 - 数量: {}", alerts.size());
            }

            log.info("告警信息处理完成 - 总数量: {}", alerts.size());
            
        } catch (Exception e) {
            log.error("处理告警信息失败 - 错误: {}", e.getMessage(), e);
        }
    }

    // ==================== 示例方法（需要根据实际业务实现） ====================



    /**
     * 发送设备注册通知
     */
    private void sendDeviceRegistrationNotification(TerminalBasicInfo basicInfo) {
        log.info("发送设备注册通知 - 设备: {}, 主机名: {}",
                basicInfo.getDeviceId(), basicInfo.getHostname());

        // 发送设备注册SSE通知（使用MAC地址）
        deviceNotificationService.sendDeviceRegisteredNotification(
                basicInfo.getIdentityMac(), basicInfo.getHostname());
    }

    /**
     * 处理设备状态变化
     */
    private void handleDeviceStatusChange(String deviceId, String statusChange, String hostname) {
        log.info("=== 处理设备状态变化 ===");
        log.info("设备: {}, 状态变化: {}, 主机名: {}", deviceId, statusChange, hostname);

        // 获取设备的MAC地址用于通知
        String identityMac = getDeviceIdentityMac(deviceId);

        switch (statusChange) {
            case "COME_ONLINE":
                log.info("设备首次上线处理 - 设备: {}", deviceId);
                // 更新数据库状态为在线
                boolean updateResult1 = terminalBasicInfoService.updateDeviceStatus(deviceId, 1);
                log.info("数据库状态更新结果: {}", updateResult1);
                // 发送上线通知
                sendDeviceStatusNotification(identityMac, "上线");
                // 发送设备上线SSE通知（使用MAC地址）
                log.info("准备发送设备首次上线SSE通知 - 设备MAC: {}, 主机名: {}", identityMac, hostname);
                deviceNotificationService.sendDeviceOnlineNotification(identityMac, hostname);
                log.info("设备首次上线SSE通知已发送 - 设备MAC: {}", identityMac);
                break;
            case "BACK_ONLINE":
                log.info("设备重新上线处理 - 设备: {}", deviceId);
                // 更新数据库状态为在线
                boolean updateResult2 = terminalBasicInfoService.updateDeviceStatus(deviceId, 1);
                log.info("数据库状态更新结果: {}", updateResult2);
                // 发送恢复通知
                sendDeviceStatusNotification(identityMac, "重新上线");
                // 发送设备上线SSE通知（使用MAC地址）
                log.info("准备发送设备重新上线SSE通知 - 设备MAC: {}, 主机名: {}", identityMac, hostname);
                deviceNotificationService.sendDeviceOnlineNotification(identityMac, hostname);
                log.info("设备重新上线SSE通知已发送 - 设备MAC: {}", identityMac);
                break;
            case "GO_OFFLINE":
                log.warn("设备离线 - 设备: {}", deviceId);
                // 更新数据库状态为离线
                terminalBasicInfoService.updateDeviceStatus(deviceId, 0);
                // 发送离线告警
                sendDeviceStatusNotification(identityMac, "离线");
                // 发送设备离线SSE通知（使用MAC地址）
                deviceNotificationService.sendDeviceOfflineNotification(identityMac, hostname);
                break;
            default:
                log.debug("设备状态无变化 - 设备: {}", deviceId);
                break;
        }

        // 发送统计信息更新通知
        sendStatisticsUpdateNotification();
    }

    /**
     * 获取设备的MAC地址
     */
    private String getDeviceIdentityMac(String deviceId) {
        try {
            var entity = terminalBasicInfoService.getByDeviceId(deviceId);
            if (entity != null && entity.getIdentityMac() != null) {
                return entity.getIdentityMac();
            }
            // 如果找不到MAC地址，返回deviceId作为备用
            log.warn("无法获取设备MAC地址，使用deviceId作为备用 - 设备: {}", deviceId);
            return deviceId;
        } catch (Exception e) {
            log.error("获取设备MAC地址失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
            return deviceId;
        }
    }

    /**
     * 发送统计信息更新通知
     */
    private void sendStatisticsUpdateNotification() {
        try {
            // 从缓存或数据库获取统计信息
            int onlineCount = terminalBasicInfoService.countOnlineDevices();
            int offlineCount = terminalBasicInfoService.countOfflineDevices();
            int alertCount = terminalAlertInfoService.countActiveAlerts();

            // 发送统计信息更新通知
            deviceNotificationService.sendStatisticsUpdateNotification(
                    onlineCount, offlineCount, alertCount);
        } catch (Exception e) {
            log.error("发送统计信息更新通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新终端在线状态（示例）
     */
    private void updateTerminalOnlineStatus(String deviceId, boolean online) {
        // TODO: 实现在线状态更新逻辑
        log.debug("更新终端在线状态 - 设备: {}, 状态: {}", deviceId, online ? "在线" : "离线");
    }

    /**
     * 保存终端指标信息到数据库（示例）
     */
    private void saveTerminalMetricInfo(TerminalMetricInfo metricInfo) {
        // TODO: 实现数据库保存逻辑
        log.debug("保存终端指标信息到数据库 - 设备: {}", metricInfo.getIdentityMac());
    }

    /**
     * 更新实时监控缓存（示例）
     */
    private void updateRealTimeMonitoringCache(TerminalMetricInfo metricInfo) {
        // TODO: 实现缓存更新逻辑
        log.debug("更新实时监控缓存 - 设备: {}", metricInfo.getIdentityMac());
    }

    /**
     * 计算性能趋势（示例）
     */
    private void calculatePerformanceTrends(TerminalMetricInfo metricInfo) {
        // TODO: 实现性能趋势计算逻辑
        log.debug("计算性能趋势 - 设备: {}", metricInfo.getIdentityMac());
    }

    /**
     * 保存告警信息到数据库（示例）
     */
    private void saveTerminalAlert(TerminalAlertInfo alert) {
        // TODO: 实现数据库保存逻辑
        log.debug("保存告警信息到数据库 - 告警ID: {}, 设备: {}", alert.getAlertId(), alert.getIdentityMac());
    }

    /**
     * 发送告警通知
     */
    private void sendAlertNotification(TerminalAlertInfo alert) {
        try {
            // TODO: 实现具体的通知逻辑（邮件、短信、钉钉等）
            log.info("发送告警通知 - 告警ID: {}, 设备MAC: {}, 类型: {}, 详情: {}",
                    alert.getAlertId(), alert.getIdentityMac(), alert.getAlertType(), alert.getAlertDetails());

            // 更新通知发送状态
            terminalAlertInfoService.updateNotificationSent(alert.getAlertId());

        } catch (Exception e) {
            log.error("发送告警通知失败 - 设备MAC: {}, 错误: {}", alert.getIdentityMac(), e.getMessage(), e);
        }
    }

    /**
     * 发送设备状态变化通知
     */
    private void sendDeviceStatusNotification(String identityMac, String statusChange) {
        try {
            // TODO: 实现具体的通知逻辑
            log.info("发送设备状态变化通知 - 设备MAC: {}, 状态变化: {}", identityMac, statusChange);

        } catch (Exception e) {
            log.error("发送设备状态变化通知失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
        }
    }

    // 设备ID -> 最后数据库更新时间的映射
    private final Map<String, LocalDateTime> lastDatabaseUpdateTimeMap = new ConcurrentHashMap<>();

    // 数据库更新的最小间隔（秒）
    private static final long MIN_DATABASE_UPDATE_INTERVAL_SECONDS = 30;

    /**
     * 判断是否应该更新数据库
     * 为了减少数据库写入频率，只有当距离上次更新超过指定时间间隔时才更新数据库
     */
    private boolean shouldUpdateDatabase(String deviceId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastUpdateTime = lastDatabaseUpdateTimeMap.get(deviceId);

        if (lastUpdateTime == null) {
            // 首次更新，记录时间并返回true
            lastDatabaseUpdateTimeMap.put(deviceId, now);
            return true;
        }

        // 计算距离上次更新的时间间隔
        long secondsSinceLastUpdate = java.time.Duration.between(lastUpdateTime, now).getSeconds();

        // 如果超过最小更新间隔，则更新数据库
        if (secondsSinceLastUpdate >= MIN_DATABASE_UPDATE_INTERVAL_SECONDS) {
            lastDatabaseUpdateTimeMap.put(deviceId, now);
            return true;
        }

        // 未达到更新间隔，不更新数据库
        return false;
    }

    /**
     * 发送告警SSE通知
     */
    private void sendAlertSSENotification(TerminalAlertInfo alert) {
        try {
            log.info("准备发送告警SSE通知 - 设备: {}, 告警类型: {}",
                    alert.getIdentityMac(), alert.getAlertType());

            // 获取设备主机名
            String hostname = "未知主机";
            try {
                TerminalBasicInfoEntity entity = terminalBasicInfoService.getByIdentityMac(alert.getIdentityMac());
                if (entity != null && entity.getHostname() != null && !entity.getHostname().isEmpty()) {
                    hostname = entity.getHostname();
                }
            } catch (Exception e) {
                log.warn("获取设备主机名失败 - 设备: {}, 错误: {}", alert.getIdentityMac(), e.getMessage());
            }

            // 使用告警对象中的告警级别，如果为空则从配置获取
            String alertLevel = alert.getAlertLevel();
            if (alertLevel == null || alertLevel.trim().isEmpty()) {
                alertLevel = determineAlertLevelFromConfig(alert.getAlertType());
                log.warn("告警级别为空，从配置获取默认级别 - 设备MAC: {}, 告警类型: {}, 级别: {}",
                        alert.getIdentityMac(), alert.getAlertType(), alertLevel);
            }

            // 发送告警通知
            deviceNotificationService.sendAlertNotification(
                    alert.getIdentityMac(),
                    hostname,
                    alert.getAlertType(), // 现在直接是中文描述
                    alertLevel,
                    alert.getAlertDetails(),
                    String.format("指标: %s, 阈值: %s, 当前值: %s",
                            alert.getMetricName(), alert.getThreshold(), alert.getCurrentValue()),
                    alert.getCurrentValue(),
                    alert.getThreshold()
            );

            log.info("告警SSE通知发送完成 - 设备MAC: {}, 告警类型: {}",
                    alert.getIdentityMac(), alert.getAlertType());

        } catch (Exception e) {
            log.error("发送告警SSE通知失败 - 设备: {}, 告警类型: {}, 错误: {}",
                    alert.getIdentityMac(), alert.getAlertType(), e.getMessage(), e);
        }
    }

    /**
     * 根据告警类型从数据库配置中确定告警级别
     */
    private String determineAlertLevelFromConfig(String alertType) {
        try {
            // 从数据库配置中获取告警级别
            MetricThresholdConfigEntity config = metricThresholdConfigService.getConfigByMetricType(alertType);
            if (config != null && config.getAlertLevel() != null) {
                return config.getAlertLevel();
            }
        } catch (Exception e) {
            log.warn("获取告警级别配置失败 - 告警类型: {}, 错误: {}", alertType, e.getMessage());
        }

        // 如果无法从配置获取，使用默认级别
        return getDefaultAlertLevel(alertType);
    }

    /**
     * 获取默认告警级别（兼容性方法）
     */
    private String getDefaultAlertLevel(String alertType) {
        if (alertType == null) {
            return "MEDIUM";
        }

        // 根据中文告警类型确定默认级别
        if (alertType.contains("CPU") || alertType.contains("内存")) {
            return "HIGH";
        } else if (alertType.contains("授权") || alertType.contains("过期")) {
            return "CRITICAL";
        } else if (alertType.contains("磁盘") || alertType.contains("存储")) {
            return "MEDIUM";
        } else {
            return "MEDIUM";
        }
    }

    /**
     * 格式化MAC地址（每两个字符添加冒号）
     */
    private String formatMacAddress(String rawMac) {
        if (rawMac == null || rawMac.length() != 12) {
            return rawMac;
        }
        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < rawMac.length(); i += 2) {
            if (i > 0) {
                formatted.append(":");
            }
            formatted.append(rawMac.substring(i, i + 2));
        }
        return formatted.toString();
    }

    /**
     * 清洗指标数据中的负值，将负值设置为0
     * 
     * @param originalData 原始JSON数据字符串
     * @return 清洗后的JSON数据字符串
     */
    private String cleanNegativeMetricValues(String originalData) {
        try {
            log.debug("开始清洗指标数据中的负值");
            
            // 解析JSON数据
            JsonNode rootNode = objectMapper.readTree(originalData);
            
            // 递归处理所有数值字段
            JsonNode cleanedNode = cleanNegativeValuesRecursively(rootNode);
            
            // 转换回JSON字符串
            String cleanedData = objectMapper.writeValueAsString(cleanedNode);
            
            log.debug("指标数据负值清洗完成");
            return cleanedData;
            
        } catch (Exception e) {
            log.error("清洗指标数据负值时发生错误: {}", e.getMessage(), e);
            // 如果清洗失败，返回原始数据
            return originalData;
        }
    }

    /**
     * 递归清洗JSON节点中的负值
     * 
     * @param node JSON节点
     * @return 清洗后的JSON节点
     */
    private JsonNode cleanNegativeValuesRecursively(JsonNode node) {
        if (node == null) {
            return node;
        }

        if (node.isObject()) {
            // 处理对象节点
            var objectNode = objectMapper.createObjectNode();
            node.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();
                
                // 递归处理子节点
                JsonNode cleanedValue = cleanNegativeValuesRecursively(fieldValue);
                objectNode.set(fieldName, cleanedValue);
            });
            return objectNode;
            
        } else if (node.isArray()) {
            // 处理数组节点
            var arrayNode = objectMapper.createArrayNode();
            for (JsonNode arrayElement : node) {
                JsonNode cleanedElement = cleanNegativeValuesRecursively(arrayElement);
                arrayNode.add(cleanedElement);
            }
            return arrayNode;
            
        } else if (node.isNumber()) {
            // 处理数值节点
            if (node.isDouble() || node.isFloat()) {
                double value = node.asDouble();
                if (value < 0) {
                    log.debug("发现负值并清洗为0: {}", value);
                    return objectMapper.getNodeFactory().numberNode(0.0);
                }
            } else if (node.isInt() || node.isLong()) {
                long value = node.asLong();
                if (value < 0) {
                    log.debug("发现负值并清洗为0: {}", value);
                    return objectMapper.getNodeFactory().numberNode(0);
                }
            }
            return node;
            
        } else {
            // 其他类型节点（字符串、布尔值等）直接返回
            return node;
        }
    }
}
